# Missing accrueInterest() Before mToken Transfers

## Description

В протоколе Malda обнаружена критическая уязвимость в функциях трансфера mToken. При выполнении операций `transfer()` и `transferFrom()` не вызывается функция `accrueInterest()` перед проверками ликвидности, что приводит к использованию устаревших данных о накопленных процентах по долгам.

## Vulnerability Details

### Проблемная цепочка вызовов:

1. **Точка входа**: `transfer()` или `transfer<PERSON><PERSON>()` в mToken
```solidity
function transfer(address dst, uint256 amount) external override nonReentrant returns (bool) {
    _transferTokens(msg.sender, msg.sender, dst, amount);  // ❌ НЕТ _accrueInterest()
    return true;
}
```

2. **Вызов проверки**: `_transferTokens()` → `beforeMTokenTransfer()` в Operator
```solidity
function _transferTokens(address spender, address src, address dst, uint256 tokens) private {
    IOperatorDefender(operator).beforeMTokenTransfer(address(this), src, dst, tokens);
    // ... остальная логика
}
```

3. **Проверка ликвидности**: `beforeMTokenTransfer()` → `_beforeRedeem()` → `_getHypotheticalAccountLiquidity()`
```solidity
function beforeMTokenTransfer(address mToken, address src, address dst, uint256 transferTokens) external override {
    require(!_paused[mToken][OperationType.Transfer], Operator_Paused());
    
    /* Get sender tokensHeld and amountOwed underlying from the mToken */
    _beforeRedeem(mToken, src, transferTokens);  // ❌ Использует устаревшие данные
    // ...
}
```

4. **Использование устаревших данных**: `getAccountSnapshot()` → `_borrowBalanceStored()`
```solidity
function getAccountSnapshot(address account) external view override returns (uint256, uint256, uint256) {
    return (accountTokens[account], _borrowBalanceStored(account), _exchangeRateStored());
    //                              ↑ Возвращает устаревший borrowBalance
}

function _borrowBalanceStored(address account) internal view returns (uint256) {
    BorrowSnapshot storage borrowSnapshot = accountBorrows[account];
    if (borrowSnapshot.principal == 0) {
        return 0;
    }
    // Использует старый borrowIndex без обновления процентов!
    uint256 principalTimesIndex = borrowSnapshot.principal * borrowIndex;
    return principalTimesIndex / borrowSnapshot.interestIndex;
}
```

### Сравнение с правильной реализацией:

Все остальные критические операции правильно вызывают `_accrueInterest()`:

```solidity
function _mint(...) internal nonReentrant {
    _accrueInterest();  // ✅ ПРАВИЛЬНО
    __mint(user, receiver, mintAmount, minAmountOut, doTransfer);
}

function _borrow(...) internal nonReentrant {
    _accrueInterest();  // ✅ ПРАВИЛЬНО
    __borrow(payable(user), payable(user), borrowAmount, doTransfer);
}

function _repay(...) internal nonReentrant returns (uint256) {
    _accrueInterest();  // ✅ ПРАВИЛЬНО
    return __repay(msg.sender, msg.sender, repayAmount, doTransfer);
}

function _liquidate(...) internal nonReentrant {
    _accrueInterest();  // ✅ ПРАВИЛЬНО
    ImToken(mTokenCollateral).accrueInterest();  // ✅ Даже для collateral!
    __liquidate(liquidator, borrower, repayAmount, mTokenCollateral, doTransfer);
}
```

### Корень проблемы:

Функция `_borrowBalanceStored()` использует сохраненный `borrowIndex`, который обновляется только при вызове `_accrueInterest()`. Без этого вызова расчет долга основывается на устаревших данных, не учитывающих накопленные проценты.

## Impact

### Высокая серьезность (HIGH severity)

1. **Блокировка легитимных трансферов**
   - Пользователи с здоровыми позициями не могут перевести mToken
   - Устаревший расчет долга показывает ложное превышение лимитов ликвидности
   - Нарушение пользовательского опыта и функциональности протокола

2. **Разрешение опасных трансферов**
   - Пользователи близкие к ликвидации могут перевести collateral
   - Реальный долг (с накопленными процентами) превышает безопасные лимиты
   - Устаревший расчет показывает ложную безопасность позиции
   - Риск неконтролируемых ликвидаций и потери средств

3. **Временная зависимость**
   - Проблема усугубляется со временем
   - Чем дольше не обновлялись проценты, тем больше расхождение
   - Особенно критично в периоды низкой активности рынка

4. **Нарушение протокольной безопасности**
   - Неточные проверки ликвидности подрывают основы lending протокола
   - Может привести к системным рискам и потере доверия пользователей

### Сценарии эксплуатации:

**Сценарий 1: Избежание ликвидации**
```
1. Пользователь имеет позицию близкую к ликвидации
2. Проходит время без активности в рынке (несколько часов)
3. Проценты накапливаются, но borrowIndex не обновляется
4. Реальный долг превышает безопасный уровень
5. _borrowBalanceStored() показывает устаревший (меньший) долг
6. Пользователь успешно переводит collateral, избегая ликвидации
7. Протокол остается с недообеспеченным долгом
```

**Сценарий 2: Блокировка здоровых позиций**
```
1. Пользователь имеет здоровую позицию с хорошим collateral ratio
2. Проценты накопились, но не обновлены
3. Проверка ликвидности использует устаревшие данные
4. Трансфер блокируется из-за ложного расчета
5. Пользователь не может управлять своими активами
```

### Рекомендации по исправлению:

Добавить вызов `accrueInterest()` в функцию `beforeMTokenTransfer()` в Operator.sol:

```solidity
function beforeMTokenTransfer(address mToken, address src, address dst, uint256 transferTokens) external override {
    require(!_paused[mToken][OperationType.Transfer], Operator_Paused());
    
    // FIX: Обновить проценты перед всеми проверками
    ImToken(mToken).accrueInterest();
    
    _beforeRedeem(mToken, src, transferTokens);
    
    // Keep the flywheel moving
    _updateMaldaSupplyIndex(mToken);
    _distributeSupplierMalda(mToken, src);
    _distributeSupplierMalda(mToken, dst);
}
```

Это обеспечит актуальность данных о процентах перед выполнением проверок ликвидности при трансферах mToken.
